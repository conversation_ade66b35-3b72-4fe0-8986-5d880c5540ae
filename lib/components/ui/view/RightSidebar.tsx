// lib/components/view/RightSidebar.tsx
import React, { useMemo } from "react"
import { ChevronRight } from "lucide-react"
import { Link } from "@lib/i18n"
import ClientImage from "./ClientImage"
import { useTranslations } from "next-intl"
import { ProjectGame } from "@/lib/types"
import { getGameLocaleContent } from "@/lib/services/api-client"
import dayjs from "dayjs"

interface RightSidebarProps {
	locale: string
	allGames?: ProjectGame[] // 所有游戏信息，用于获取关联游戏的详细信息
}

export const RightSidebar: React.FC<RightSidebarProps> = ({
	locale,
	allGames = [],
}) => {
	const t = useTranslations("Game")

	// 处理热门游戏（随机选择最多10个游戏）
	const hotGames = useMemo(() => {
		if (allGames.length < 5) return []

		// 复制数组并随机打乱
		const shuffled = [...allGames].sort(() => 0.5 - Math.random())
		return shuffled.slice(0, 5)
	}, [allGames])
  console.log("hotgames =====", hotGames.length)

	// 处理最新游戏（按更新时间倒序排列）
	const latestGames = useMemo(() => {
		return [...allGames]
			.sort((a, b) => {
				// 假设游戏信息中有更新时间字段，如果没有可以根据实际情况调整
				const timeA = dayjs(a.updateTime || new Date())
				const timeB = dayjs(b.updateTime || new Date())
				return timeB.diff(timeA)
			})
			.slice(0, 10)
	}, [allGames])

	// 渲染游戏卡片的函数
	const renderGameCard = (game: ProjectGame) => {
		// 获取图片URL
		const imageUrl = game.gameImages?.[0]
		const gameContent = getGameLocaleContent(locale, game)
		return (
			<Link
				href={game.slug}
				key={game.id}
				className="group relative overflow-hidden rounded-lg transition-all duration-300 hover:shadow-md flex flex-col"
			>
				{/* 使用固定高度的容器替代aspect-ratio */}
				<div className="h-20 overflow-hidden rounded-lg">
					<ClientImage
						src={imageUrl || ""}
						alt={gameContent.gameName}
						className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
						fallbackSrc="/placeholder-image.png"
					/>
				</div>
				{/* 游戏标题 - 紧贴图片 */}
				<div className="py-1 px-1 text-center mt-0">
					<span
						className="text-xs font-medium text-gray-700 dark:text-gray-300 line-clamp-1"
						title={gameContent.gameName}
					>
						{gameContent.gameName}
					</span>
				</div>
				{/* 悬停效果覆盖层 */}
				<div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-2">
					<span className="text-sm font-medium text-white truncate">
						{gameContent.gameName}
					</span>
				</div>
			</Link>
		)
	}

	// 渲染游戏列表的函数（带广告插入）
	const renderGameList = (games: ProjectGame[]) => {
		if (!games || games.length === 0) return null

		const result = []

		// 每三行插入一个广告
		for (let i = 0; i < games.length; i++) {
			// 每6个游戏（3行）后插入一个广告
			if (i > 0 && i % 6 === 0) {
				result.push(
					<div key={`ad-${i / 6}`} className="col-span-2 my-2">
						<div className="w-full aspect-w-16 aspect-h-9 overflow-hidden rounded-lg bg-gray-200 flex items-center justify-center text-xs text-gray-500 hover:bg-gray-300 transition-all duration-300">
							广告位
						</div>
					</div>,
				)
			}
			result.push(renderGameCard(games[i] as ProjectGame))
		}

		return result
	}

	return (
		// Make sidebar sticky on large screens
		<aside className="lg:top-24 lg:self-start h-fit space-y-6 hidden md:block">
			{/* 1. 广告位 */}
			<div className="rounded-lg bg-card p-4 shadow-sm overflow-hidden">
				<div className="w-full aspect-w-16 aspect-h-9 overflow-hidden rounded-lg bg-gray-200 flex items-center justify-center text-xs text-gray-500 hover:bg-gray-300 transition-all duration-300">
					广告位
				</div>
			</div>

			{/* 2. 热门游戏 */}
			{hotGames.length > 0 && (
				<div className="rounded-lg bg-card p-4 shadow-sm overflow-hidden">
					<h3 className="mb-4 text-lg font-semibold text-card-foreground border-b pb-2 border-border flex items-center">
						<span className="inline-block w-1 h-5 bg-primary rounded-full mr-2"></span>
						{t("hotGamesTitle") || "热门游戏"}
					</h3>
					<div className="grid grid-cols-2 gap-4">
						{renderGameList(hotGames)}
					</div>
					<div className="mt-4 text-center">
						<Link
							href="/games"
							className="inline-flex items-center text-primary hover:text-primary/80 text-sm font-medium group"
						>
							{t("viewMoreGames") || "查看更多游戏"}
							<ChevronRight className="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300" />
						</Link>
					</div>
				</div>
			)}

			{/* 3. 最新游戏 */}
			{latestGames.length > 0 && (
				<div className="rounded-lg bg-card p-4 shadow-sm overflow-hidden">
					<h3 className="mb-4 text-lg font-semibold text-card-foreground border-b pb-2 border-border flex items-center">
						<span className="inline-block w-1 h-5 bg-primary rounded-full mr-2"></span>
						{t("latestGamesTitle") || "最新游戏"}
					</h3>
					<div className="grid grid-cols-2 gap-4">
						{renderGameList(latestGames)}
					</div>
					<div className="mt-4 text-center">
						<Link
							href="/games"
							className="inline-flex items-center text-primary hover:text-primary/80 text-sm font-medium group"
						>
							{t("viewMoreGames") || "查看更多游戏"}
							<ChevronRight className="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300" />
						</Link>
					</div>
				</div>
			)}
		</aside>
	)
}

export default RightSidebar
